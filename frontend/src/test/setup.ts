/**
 * Test Setup Configuration
 * 
 * This file configures the global test environment, including DOM polyfills,
 * testing library extensions, and global mocks required for testing the
 * React/TypeScript frontend application.
 */

import "@testing-library/jest-dom"
import { beforeAll, afterEach, afterAll, vi } from "vitest"
import { cleanup } from "@testing-library/react"

// Extend Vitest's expect with jest-dom matchers
import type { TestingLibraryMatchers } from "@testing-library/jest-dom/matchers"

declare module "vitest" {
  interface Assertion<T = any> extends TestingLibraryMatchers<T, void> {}
  interface AsymmetricMatchersContaining extends TestingLibraryMatchers<any, void> {}
}

// Global test setup
beforeAll(() => {
  // Mock window.matchMedia for components that use media queries
  Object.defineProperty(window, "matchMedia", {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  })

  // Mock window.ResizeObserver for components that observe element resizing
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock IntersectionObserver for components that use intersection detection
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }))

  // Mock scrollTo for components that programmatically scroll
  window.scrollTo = vi.fn()

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  }
  Object.defineProperty(window, "localStorage", {
    value: localStorageMock,
  })

  // Mock sessionStorage
  const sessionStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  }
  Object.defineProperty(window, "sessionStorage", {
    value: sessionStorageMock,
  })

  // Mock fetch for API calls
  global.fetch = vi.fn()

  // Mock console methods to reduce noise in tests
  vi.spyOn(console, "warn").mockImplementation(() => {})
  vi.spyOn(console, "error").mockImplementation(() => {})
})

// Clean up after each test
afterEach(() => {
  // Clean up DOM after each test
  cleanup()
  
  // Clear all mocks
  vi.clearAllMocks()
  
  // Reset localStorage mock
  if (window.localStorage) {
    ;(window.localStorage.clear as any).mockClear()
    ;(window.localStorage.getItem as any).mockClear()
    ;(window.localStorage.setItem as any).mockClear()
    ;(window.localStorage.removeItem as any).mockClear()
  }
  
  // Reset sessionStorage mock
  if (window.sessionStorage) {
    ;(window.sessionStorage.clear as any).mockClear()
    ;(window.sessionStorage.getItem as any).mockClear()
    ;(window.sessionStorage.setItem as any).mockClear()
    ;(window.sessionStorage.removeItem as any).mockClear()
  }
})

// Global cleanup
afterAll(() => {
  // Restore all mocks
  vi.restoreAllMocks()
})

// Global test utilities and constants
export const TEST_CONSTANTS = {
  // Mock API endpoints
  API_BASE_URL: "http://localhost:8000",
  
  // Mock user data
  MOCK_USER_ID: "test-user-123",
  MOCK_SESSION_ID: "test-session-456",
  
  // Mock repository data
  MOCK_REPOSITORY_URL: "https://github.com/test/repo",
  MOCK_BRANCH: "main",
  MOCK_COMMIT_SHA: "a1b2c3d4e5f6789012345678901234567890abcd",
  
  // Test timeouts
  DEFAULT_TIMEOUT: 5000,
  ASYNC_TIMEOUT: 10000,
} as const

// Export type for test constants
export type TestConstants = typeof TEST_CONSTANTS
